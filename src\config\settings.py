from pathlib import Path
from typing import Optional, Dict, Any, Literal
from pydantic import HttpUrl, field_validator, Field as Pydantic<PERSON>ield
from pydantic_settings import BaseSettings, SettingsConfigDict
from enum import Enum

class EnvironmentType(str, Enum):
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"

class APIConfig(BaseSettings):
    """Configuration for API connections."""
    base_url: Optional[HttpUrl] = None
    api_key: Optional[str] = None
    timeout: int = 30
    max_retries: int = 3
    retry_delay: float = 1.0

    class Config:
        env_prefix = "API_"

class DatabaseConfig(BaseSettings):
    """Configuration for database connections."""
    url: str = "sqlite:///./research.db"
    echo: bool = False
    pool_size: int = 5
    max_overflow: int = 10

    class Config:
        env_prefix = "DB_"

class LoggingConfig(BaseSettings):
    """Configuration for logging."""
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file: Optional[Path] = None
    max_size_mb: int = 10
    backup_count: int = 5

    class Config:
        env_prefix = "LOG_"

class Settings(BaseSettings):
    """Main settings class that loads configuration from environment variables."""
    # Environment
    env: EnvironmentType = EnvironmentType.DEVELOPMENT
    debug: bool = False

    # Application
    app_name: str = "Deep Research Tool"
    app_version: str = "0.1.0"
    description: str = "A tool for deep research using AI APIs"

    # Paths
    base_dir: Path = Path(__file__).parent.parent.parent
    data_dir: Path = base_dir / "data"
    cache_dir: Path = base_dir / ".cache"

    # API configurations
    perplexity: APIConfig = PydanticField(default_factory=APIConfig)
    ollama: APIConfig = PydanticField(default_factory=APIConfig)

    # Database
    database: DatabaseConfig = PydanticField(default_factory=DatabaseConfig)

    # Logging
    logging: LoggingConfig = PydanticField(default_factory=LoggingConfig)

    # Research settings
    default_max_depth: int = 3
    default_max_breadth: int = 3
    default_research_depth: str = "moderate"

    # Rate limiting
    requests_per_minute: int = 60

    # Batch Processing Settings
    optimal_workers: int = PydanticField(
        default=5,
        description="Optimal number of worker processes",
        validation_alias="OPTIMAL_WORKERS"
    )
    batch_strategy: Literal["fixed", "dynamic", "adaptive", "performance", "resource_aware", "hybrid"] = PydanticField(
        default="dynamic",
        description="Batch processing strategy (fixed, dynamic, adaptive, performance, resource_aware, hybrid)",
        validation_alias="BATCH_STRATEGY"
    )
    batch_adaptive_threshold: float = PydanticField(
        default=0.8,
        description="Threshold for adaptive batch strategy",
        validation_alias="BATCH_ADAPTIVE_THRESHOLD"
    )
    batch_max_retries: int = PydanticField(
        default=3,
        description="Maximum number of retries for failed batches",
        validation_alias="BATCH_MAX_RETRIES"
    )
    batch_size_min: int = PydanticField(
        default=1,
        description="Minimum batch size",
        validation_alias="BATCH_SIZE_MIN"
    )
    batch_size_max: int = PydanticField(
        default=100,
        description="Maximum batch size",
        validation_alias="BATCH_SIZE_MAX"
    )
    batch_size_factor: float = PydanticField(
        default=0.5,
        description="Factor for calculating batch size based on workers",
        validation_alias="BATCH_SIZE_FACTOR"
    )
    batch_retry_delay: int = PydanticField(
        default=5,
        description="Delay in seconds between batch retries",
        validation_alias="BATCH_RETRY_DELAY"
    )

    # Additional batch processing settings
    batch_timeout: int = PydanticField(
        default=300,
        description="Timeout for batch processing in seconds",
        validation_alias="BATCH_TIMEOUT"
    )
    batch_ram_limit_gb: float = PydanticField(
        default=8.0,
        description="Maximum RAM usage per batch in GB",
        validation_alias="BATCH_RAM_LIMIT_GB"
    )
    batch_gpu_limit_gb: float = PydanticField(
        default=4.0,
        description="Maximum GPU memory usage per batch in GB",
        validation_alias="BATCH_GPU_LIMIT_GB"
    )

    # Performance strategy weights
    batch_performance_weight_time: float = PydanticField(
        default=0.4,
        description="Weight for time in performance strategy",
        validation_alias="BATCH_PERFORMANCE_WEIGHT_TIME"
    )
    batch_performance_weight_memory: float = PydanticField(
        default=0.2,
        description="Weight for memory in performance strategy",
        validation_alias="BATCH_PERFORMANCE_WEIGHT_MEMORY"
    )
    batch_performance_weight_cpu: float = PydanticField(
        default=0.2,
        description="Weight for CPU in performance strategy",
        validation_alias="BATCH_PERFORMANCE_WEIGHT_CPU"
    )
    batch_performance_weight_gpu: float = PydanticField(
        default=0.1,
        description="Weight for GPU in performance strategy",
        validation_alias="BATCH_PERFORMANCE_WEIGHT_GPU"
    )
    batch_performance_weight_success: float = PydanticField(
        default=0.1,
        description="Weight for success rate in performance strategy",
        validation_alias="BATCH_PERFORMANCE_WEIGHT_SUCCESS"
    )

    # Resource strategy weights
    batch_resource_weight_memory: float = PydanticField(
        default=0.3,
        description="Weight for memory in resource strategy",
        validation_alias="BATCH_RESOURCE_WEIGHT_MEMORY"
    )
    batch_resource_weight_cpu: float = PydanticField(
        default=0.3,
        description="Weight for CPU in resource strategy",
        validation_alias="BATCH_RESOURCE_WEIGHT_CPU"
    )
    batch_resource_weight_gpu: float = PydanticField(
        default=0.1,
        description="Weight for GPU in resource strategy",
        validation_alias="BATCH_RESOURCE_WEIGHT_GPU"
    )
    batch_resource_weight_success: float = PydanticField(
        default=0.1,
        description="Weight for success rate in resource strategy",
        validation_alias="BATCH_RESOURCE_WEIGHT_SUCCESS"
    )

    # Adaptive strategy weights
    batch_adaptive_weight_time: float = PydanticField(
        default=0.3,
        description="Weight for time in adaptive strategy",
        validation_alias="BATCH_ADAPTIVE_WEIGHT_TIME"
    )
    batch_adaptive_weight_memory: float = PydanticField(
        default=0.2,
        description="Weight for memory in adaptive strategy",
        validation_alias="BATCH_ADAPTIVE_WEIGHT_MEMORY"
    )
    batch_adaptive_weight_cpu: float = PydanticField(
        default=0.2,
        description="Weight for CPU in adaptive strategy",
        validation_alias="BATCH_ADAPTIVE_WEIGHT_CPU"
    )
    batch_adaptive_weight_gpu: float = PydanticField(
        default=0.2,
        description="Weight for GPU in adaptive strategy",
        validation_alias="BATCH_ADAPTIVE_WEIGHT_GPU"
    )
    batch_adaptive_weight_success: float = PydanticField(
        default=0.1,
        description="Weight for success rate in adaptive strategy",
        validation_alias="BATCH_ADAPTIVE_WEIGHT_SUCCESS"
    )

    # Hybrid strategy weights
    batch_hybrid_weight_time: float = PydanticField(
        default=0.3,
        description="Weight for time in hybrid strategy",
        validation_alias="BATCH_HYBRID_WEIGHT_TIME"
    )
    batch_hybrid_weight_memory: float = PydanticField(
        default=0.2,
        description="Weight for memory in hybrid strategy",
        validation_alias="BATCH_HYBRID_WEIGHT_MEMORY"
    )
    batch_hybrid_weight_cpu: float = PydanticField(
        default=0.2,
        description="Weight for CPU in hybrid strategy",
        validation_alias="BATCH_HYBRID_WEIGHT_CPU"
    )
    batch_hybrid_weight_gpu: float = PydanticField(
        default=0.2,
        description="Weight for GPU in hybrid strategy",
        validation_alias="BATCH_HYBRID_WEIGHT_GPU"
    )
    batch_hybrid_weight_success: float = PydanticField(
        default=0.1,
        description="Weight for success rate in hybrid strategy",
        validation_alias="BATCH_HYBRID_WEIGHT_SUCCESS"
    )

    # Additional settings
    openai_endpoint: Optional[str] = PydanticField(
        default=None,
        description="OpenAI compatible API endpoint",
        validation_alias="OPENAI_ENDPOINT"
    )
    optimal_ram_gb: Optional[float] = PydanticField(
        default=None,
        description="Optimal RAM in GB",
        validation_alias="OPTIMAL_RAM_GB"
    )
    optimal_gpu_count: Optional[int] = PydanticField(
        default=None,
        description="Optimal GPU count",
        validation_alias="OPTIMAL_GPU_COUNT"
    )

    model_config = SettingsConfigDict(
        env_nested_delimiter="__",
        env_file=".env",
        env_file_encoding="utf-8"
    )

    @field_validator('data_dir', 'cache_dir', mode='before')
    def create_dirs(cls, v):
        """Ensure directories exist."""
        path = Path(v)
        path.mkdir(parents=True, exist_ok=True)
        return path

# Global settings instance (lazy initialization)
_settings: Optional[Settings] = None

def get_settings() -> Settings:
    """Get the settings instance."""
    global _settings
    if _settings is None:
        _settings = Settings()
    return _settings

def configure_settings(**overrides: Dict[str, Any]) -> None:
    """Update settings with overrides."""
    global _settings
    current_settings = get_settings()
    _settings = Settings(**{**current_settings.model_dump(), **overrides})
