from typing import Dict, Any, Optional
import GPUtil
import cpuinfo
import psutil
import json
import os
from ..exceptions import ConfigurationError
from pathlib import Path
from .batch_monitor import BatchPerformanceMonitor

ENV_PATH = Path(__file__).parent.parent.parent / '.env'

def update_env_file(new_vars: dict):
    """Update or append key-value pairs in the .env file without breaking comments or other keys."""
    if not ENV_PATH.exists():
        lines = []
    else:
        lines = ENV_PATH.read_text(encoding='utf-8').splitlines()
    env_dict = {}
    for line in lines:
        if '=' in line and not line.strip().startswith('#'):
            k, v = line.split('=', 1)
            env_dict[k.strip()] = v.strip()
    # Update/add new vars
    env_dict.update(new_vars)
    # Reconstruct lines, preserving comments
    new_lines = []
    for line in lines:
        if '=' in line and not line.strip().startswith('#'):
            k = line.split('=', 1)[0].strip()
            if k in new_vars:
                new_lines.append(f"{k}={new_vars[k]}")
                new_vars.pop(k)
            else:
                new_lines.append(line)
        else:
            new_lines.append(line)
    for k, v in new_vars.items():
        new_lines.append(f"{k}={v}")
    ENV_PATH.write_text('\n'.join(new_lines) + '\n', encoding='utf-8')

class HardwareService:
    def __init__(self, settings_file: str = "hardware_settings.json"):
        self.settings_file = settings_file
        self.batch_monitor = BatchPerformanceMonitor()

    def benchmark_optimal_workers(self, max_threads):
        """Empirically determine the fastest OPTIMAL_WORKERS value by benchmarking."""
        import time
        from concurrent.futures import ThreadPoolExecutor

        def cpu_task():
            # Simple CPU-bound operation (sum of squares)
            return sum(i*i for i in range(1000000))

        worker_options = [1, 2, 4, 8, 16, max_threads]
        worker_options = sorted(set([w for w in worker_options if w <= max_threads]))
        best_time = float('inf')
        best_workers = 1
        timings = {}
        print("[Benchmark] Testing worker counts:", worker_options)
        for workers in worker_options:
            start = time.time()
            with ThreadPoolExecutor(max_workers=workers) as executor:
                list(executor.map(lambda _: cpu_task(), range(workers)))
            elapsed = time.time() - start
            timings[workers] = elapsed
            print(f"[Benchmark] Workers: {workers}, Time: {elapsed:.4f}s")
            if elapsed < best_time:
                best_time = elapsed
                best_workers = workers

            # Monitor batch performance
            self.batch_monitor.start_batch(workers, workers)
            self.batch_monitor.end_batch(True)

        print(f"[Benchmark] Fastest: {best_workers} workers ({best_time:.4f}s)")
        return best_workers, timings

    def _optimize_batch_settings(self):
        """Optimize batch settings based on monitoring data."""
        # Load existing batch monitoring data
        self.batch_monitor.load_from_file()

        # If we have enough data, use it to optimize batch settings
        if len(self.batch_monitor.batch_times) > 5:  # Need at least 5 data points
            optimal_size, metrics = self.batch_monitor.get_optimal_batch_size()
            return optimal_size, metrics

        # If not enough data, use system resources to estimate
        cpu_cores = psutil.cpu_count(logical=False)
        ram_gb = psutil.virtual_memory().total / (1024 ** 3)

        # Handle case where cpu_cores might be None
        if cpu_cores is None:
            cpu_cores = psutil.cpu_count(logical=True) or 4  # Fallback to logical cores or default to 4

        # Start with a conservative estimate
        optimal_size = min(
            int(cpu_cores * 0.7),  # 70% of CPU cores
            int(ram_gb / 2),       # 2GB per batch
            100                    # Maximum batch size
        )

        return optimal_size, {
            'avg_time': 0,
            'avg_memory': ram_gb * 0.5,
            'avg_cpu': 70.0,
            'avg_gpu': 0.0,
            'success_rate': 0.95
        }

    def run_calibration(self) -> Dict[str, Any]:
        """Run hardware calibration and save results."""
        try:
            # CPU Info
            cpu = cpuinfo.get_cpu_info()
            cpu_name = cpu.get('brand_raw', 'Unknown')
            cpu_cores = psutil.cpu_count(logical=False)
            cpu_threads = psutil.cpu_count(logical=True)

            # Handle case where cpu_cores might be None
            if cpu_cores is None:
                cpu_cores = cpu_threads or 4  # Fallback to logical cores or default to 4

            # RAM Info
            ram_gb = round(psutil.virtual_memory().total / (1024 ** 3), 2)

            # GPU Info
            gpus = GPUtil.getGPUs()
            gpu_info = []
            for gpu in gpus:
                gpu_info.append({
                    'name': gpu.name,
                    'memory_total_gb': round(gpu.memoryTotal / 1024, 2),
                    'memory_free_gb': round(gpu.memoryFree / 1024, 2),
                    'memory_used_gb': round(gpu.memoryUsed / 1024, 2),
                    'load': gpu.load
                })

            result = {
                'cpu': {'name': cpu_name, 'cores': cpu_cores, 'threads': cpu_threads},
                'ram_gb': ram_gb,
                'gpus': gpu_info
            }

            # Compute optimal resource usage (70%)
            optimal_workers = max(1, int(cpu_cores * 0.7))
            optimal_ram_gb = round(ram_gb * 0.7, 2)
            optimal_gpu_count = max(1, int(len(gpu_info) * 0.7)) if gpu_info else 0

            # Optimize batch settings based on monitoring data
            optimal_batch_size, batch_metrics = self._optimize_batch_settings()
            update_env_file({
                'OPTIMAL_BATCH_SIZE': str(optimal_batch_size),
                'BATCH_AVERAGE_TIME': f"{batch_metrics.get('avg_time', 0):.2f}",
                'BATCH_AVERAGE_MEMORY': f"{batch_metrics.get('avg_memory', 0):.2f}",
                'BATCH_AVERAGE_CPU': f"{batch_metrics.get('avg_cpu', 0):.2f}",
                'BATCH_AVERAGE_GPU': f"{batch_metrics.get('avg_gpu', 0):.2f}",
                'BATCH_SUCCESS_RATE': f"{batch_metrics.get('success_rate', 0):.2f}"
            })
            print(f"[Calibration] Batch optimization: Size={optimal_batch_size}, Success Rate={batch_metrics.get('success_rate', 0):.2f}")

            # Benchmark to find optimal worker count
            optimal_workers, timings = self.benchmark_optimal_workers(cpu_cores)

            # Save results
            save = input("Save calibration results for future runs? (y/n): ").strip().lower()
            if save == 'y':
                with open(self.settings_file, 'w', encoding='utf-8') as f:
                    json.dump(result, f, indent=2)
                # Update .env with optimal values
                update_env_file({
                    'OPTIMAL_WORKERS': str(optimal_workers),
                    'OPTIMAL_RAM_GB': str(optimal_ram_gb),
                    'OPTIMAL_GPU_COUNT': str(optimal_gpu_count)
                })
                print(f"[Calibration] .env updated: OPTIMAL_WORKERS={optimal_workers}, OPTIMAL_RAM_GB={optimal_ram_gb}, OPTIMAL_GPU_COUNT={optimal_gpu_count}")
                print(f"[Benchmark] Worker timings: {timings}")

            return result

        except Exception as e:
            raise ConfigurationError(f"Hardware calibration failed: {str(e)}")

    def load_calibration(self) -> Optional[Dict[str, Any]]:
        """Load previously saved calibration results."""
        if os.path.exists(self.settings_file):
            try:
                with open(self.settings_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                raise ConfigurationError(f"Failed to load calibration: {str(e)}")
        return None
