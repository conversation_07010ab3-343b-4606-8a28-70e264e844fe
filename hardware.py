import GPUtil
import cpuinfo
import psutil
import json
import os
from rich.console import Console

console = Console()
SETTINGS_FILE = "hardware_settings.json"

def run_calibration():
    console.print("[bold blue]Running hardware optimizer calibration...[/bold blue]")
    # CPU Info
    cpu = cpuinfo.get_cpu_info()
    cpu_name = cpu.get('brand_raw', 'Unknown')
    cpu_cores = psutil.cpu_count(logical=False)
    cpu_threads = psutil.cpu_count(logical=True)

    # Handle case where cpu_cores might be None
    if cpu_cores is None:
        cpu_cores = cpu_threads or 4  # Fallback to logical cores or default to 4
    # RAM Info
    ram_gb = round(psutil.virtual_memory().total / (1024 ** 3), 2)
    # GPU Info
    gpus = GPUtil.getGPUs()
    gpu_info = []
    for gpu in gpus:
        gpu_info.append({
            'name': gpu.name,
            'memory_total_gb': round(gpu.memoryTotal / 1024, 2),
            'memory_free_gb': round(gpu.memoryFree / 1024, 2),
            'memory_used_gb': round(gpu.memoryUsed / 1024, 2),
            'load': gpu.load
        })
    result = {
        'cpu': {'name': cpu_name, 'cores': cpu_cores, 'threads': cpu_threads},
        'ram_gb': ram_gb,
        'gpus': gpu_info
    }
    console.print(f"[green]CPU:[/green] {cpu_name} ({cpu_cores} cores, {cpu_threads} threads)")
    console.print(f"[green]RAM:[/green] {ram_gb} GB")
    if gpu_info:
        for idx, gpu in enumerate(gpu_info):
            console.print(f"[green]GPU {idx+1}:[/green] {gpu['name']} ({gpu['memory_total_gb']} GB total, {gpu['memory_free_gb']} GB free, {gpu['memory_used_gb']} GB used, load: {gpu['load']*100:.1f}%)")
    else:
        console.print("[yellow]No compatible GPU detected.[/yellow]")
    # Save results
    save = input("Save calibration results for future runs? (y/n): ").strip().lower()
    if save == 'y':
        with open(SETTINGS_FILE, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2)
        console.print(f"[bold green]Calibration results saved to {SETTINGS_FILE}.[/bold green]")
    return result

def load_calibration():
    if os.path.exists(SETTINGS_FILE):
        with open(SETTINGS_FILE, 'r', encoding='utf-8') as f:
            return json.load(f)
    return None
